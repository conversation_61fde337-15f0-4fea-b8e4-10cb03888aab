// AL-SALAMAT Admin Panel Script
// Complete admin management system

class AdminPanel {
    constructor() {
        this.database = null;
        this.storage = null;
        this.auth = null;
        this.currentUser = null;
        this.isLoading = false;
        this.init();
    }

    async init() {
        try {
            console.log('🚀 Initializing Admin Panel...');
            
            // Initialize Firebase
            await this.initializeFirebase();
            
            // Check authentication
            await this.checkAuthentication();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial data
            await this.loadAllData();
            
            // Hide loading overlay
            this.hideLoading();
            
            console.log('✅ Admin Panel initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing admin panel:', error);
            this.showMessage('خطأ في تحميل لوحة الإدارة', 'error');
        }
    }

    async initializeFirebase() {
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }

        this.database = firebase.database();
        this.storage = firebase.storage();
        this.auth = firebase.auth();

        console.log('🔥 Firebase initialized');

        // Test Firebase connection
        this.testFirebaseConnection();
    }

    async testFirebaseConnection() {
        try {
            // Test database connection
            const connectedRef = this.database.ref('.info/connected');
            const snapshot = await connectedRef.once('value');
            const connected = snapshot.val();

            if (connected) {
                console.log('✅ Firebase Database connection successful');
            } else {
                console.warn('⚠️ Firebase Database connection status: disconnected');
            }

            // Test storage connection by creating a reference
            const storageRef = this.storage.ref();
            console.log('✅ Firebase Storage reference created successfully');

            // Test if we can access storage bucket
            try {
                const testRef = this.storage.ref('test');
                console.log('✅ Firebase Storage bucket accessible');

                // Test storage rules by trying to create a reference
                const serviceTestRef = this.storage.ref('serviceImages/test.txt');
                console.log('✅ Service images folder accessible');
            } catch (storageError) {
                console.warn('⚠️ Firebase Storage bucket access issue:', storageError);
            }

        } catch (error) {
            console.error('❌ Firebase connection test failed:', error);
            this.showMessage('تحذير: مشكلة في الاتصال بـ Firebase', 'error');
        }
    }

    async checkAuthentication() {
        return new Promise((resolve) => {
            this.auth.onAuthStateChanged(async (user) => {
                if (user) {
                    this.currentUser = user;
                    console.log('👤 User authenticated:', user.email || user.uid);

                    // Check if user has admin privileges
                    if (await this.verifyAdminAccess(user)) {
                        resolve();
                    } else {
                        console.log('❌ User does not have admin privileges');
                        alert('ليس لديك صلاحيات للوصول إلى لوحة الإدارة');
                        window.location.href = 'index.html';
                    }
                } else {
                    console.log('❌ User not authenticated');
                    // Try to authenticate with admin credentials
                    await this.tryAdminAuthentication();
                }
            });
        });
    }

    async verifyAdminAccess(user) {
        try {
            // Check if user email is admin
            const adminEmails = ['<EMAIL>'];
            if (user.email && adminEmails.includes(user.email)) {
                return true;
            }

            // Check if user has admin role in database
            if (user.uid) {
                const userSnapshot = await this.database.ref(`users/${user.uid}`).once('value');
                const userData = userSnapshot.val();
                if (userData && userData.role === 'admin') {
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('Error verifying admin access:', error);
            return false;
        }
    }

    async tryAdminAuthentication() {
        try {
            console.log('🔑 Attempting admin authentication...');

            // Try to sign in with admin credentials
            const adminEmail = '<EMAIL>';
            const adminPassword = 'admin123456'; // You should change this

            const userCredential = await this.auth.signInWithEmailAndPassword(adminEmail, adminPassword);
            this.currentUser = userCredential.user;
            console.log('✅ Admin authentication successful');

        } catch (error) {
            console.error('❌ Admin authentication failed:', error);

            // If admin account doesn't exist, create it
            if (error.code === 'auth/user-not-found') {
                await this.createAdminAccount();
            } else {
                // Redirect to login page
                window.location.href = 'login.html';
            }
        }
    }

    async createAdminAccount() {
        try {
            console.log('👤 Creating admin account...');

            const adminEmail = '<EMAIL>';
            const adminPassword = 'admin123456';

            const userCredential = await this.auth.createUserWithEmailAndPassword(adminEmail, adminPassword);
            const user = userCredential.user;

            // Save admin user data
            await this.database.ref(`users/${user.uid}`).set({
                email: adminEmail,
                name: 'مدير النظام',
                role: 'admin',
                createdAt: new Date().toISOString(),
                lastLogin: new Date().toISOString()
            });

            this.currentUser = user;
            console.log('✅ Admin account created successfully');

        } catch (error) {
            console.error('❌ Error creating admin account:', error);
            alert('خطأ في إنشاء حساب المدير. يرجى المحاولة مرة أخرى.');
            window.location.href = 'index.html';
        }
    }

    setupEventListeners() {
        // Company Info Form
        document.getElementById('company-info-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCompanyInfo();
        });

        // Add Branch Form
        document.getElementById('add-branch-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addBranch();
        });

        // Edit Branch Form
        document.getElementById('edit-branch-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.updateBranch();
        });

        // Contact Form
        document.getElementById('contact-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveContactInfo();
        });

        // About Form
        document.getElementById('about-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveAboutInfo();
        });

        // Upload Image Form
        document.getElementById('upload-image-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.uploadImage();
        });

        // Settings Form
        document.getElementById('settings-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettings();
        });

        // Banner Form
        document.getElementById('banner-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveBannerSettings();
        });

        // Gallery Upload Form
        document.getElementById('upload-image-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.uploadGalleryImage();
        });

        console.log('📝 Event listeners setup complete');
    }

    async loadAllData() {
        try {
            await Promise.all([
                this.loadCompanyInfo(),
                this.loadAboutInfo(),
                this.loadBranches(),
                this.loadContactInfo(),
                this.loadGallery(),
                this.loadGalleryImages(),
                this.loadSettings(),
                this.loadBannerSettings(),
                this.loadHeroImages(),
                this.loadServiceImages(),
                this.loadUsers(),
                this.loadMessages()
            ]);
            console.log('📊 All data loaded');
        } catch (error) {
            console.error('❌ Error loading data:', error);
        }
    }

    // Company Info Management
    async loadCompanyInfo() {
        try {
            const snapshot = await this.database.ref('siteContent').once('value');
            const data = snapshot.val();
            
            if (data) {
                document.getElementById('company-title').value = data.title || '';
                document.getElementById('company-subtitle').value = data.subtitle || '';
                document.getElementById('company-description').value = data.description || '';
            }
        } catch (error) {
            console.error('❌ Error loading company info:', error);
        }
    }

    async saveCompanyInfo() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('company-info-form'));
            const data = {
                title: formData.get('title'),
                subtitle: formData.get('subtitle'),
                description: formData.get('description'),
                updatedAt: new Date().toISOString()
            };

            await this.database.ref('siteContent').set(data);
            this.showMessage('تم حفظ معلومات الشركة بنجاح', 'success');
            
        } catch (error) {
            console.error('❌ Error saving company info:', error);
            this.showMessage('خطأ في حفظ معلومات الشركة', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // About Info Management
    async loadAboutInfo() {
        try {
            const snapshot = await this.database.ref('aboutSection').once('value');
            const data = snapshot.val();

            if (data) {
                document.getElementById('about-title-input').value = data.title || '';
                document.getElementById('about-description-input').value = data.description || '';
            }
        } catch (error) {
            console.error('❌ Error loading about info:', error);
        }
    }

    async saveAboutInfo() {
        try {
            this.showLoading();

            const formData = new FormData(document.getElementById('about-form'));
            const data = {
                title: formData.get('title'),
                description: formData.get('description'),
                updatedAt: new Date().toISOString()
            };

            await this.database.ref('aboutSection').set(data);
            this.showMessage('تم حفظ معلومات "من نحن" بنجاح', 'success');

        } catch (error) {
            console.error('❌ Error saving about info:', error);
            this.showMessage('خطأ في حفظ معلومات "من نحن"', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Branches Management
    async loadBranches() {
        try {
            const snapshot = await this.database.ref('branches').once('value');
            const data = snapshot.val();
            const container = document.getElementById('branches-grid');
            const noData = document.getElementById('no-branches');
            
            container.innerHTML = '';
            
            if (data && Object.keys(data).length > 0) {
                noData.style.display = 'none';
                
                Object.entries(data).forEach(([id, branch]) => {
                    const branchElement = this.createBranchElement(id, branch);
                    container.appendChild(branchElement);
                });
            } else {
                noData.style.display = 'block';
            }
        } catch (error) {
            console.error('❌ Error loading branches:', error);
        }
    }

    createBranchElement(id, branch) {
        const div = document.createElement('div');
        div.className = 'branch-item';
        div.innerHTML = `
            <h4>${this.escapeHtml(branch.name)}</h4>
            <p><strong>العنوان:</strong> ${this.escapeHtml(branch.address)}</p>
            <p><strong>الهاتف:</strong> ${this.escapeHtml(branch.phone || 'غير محدد')}</p>
            <div class="branch-actions">
                <button class="admin-btn primary" onclick="adminPanel.editBranch('${id}')">✏️ تعديل</button>
                <button class="admin-btn danger" onclick="adminPanel.deleteBranch('${id}')">🗑️ حذف</button>
            </div>
        `;
        return div;
    }

    async addBranch() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('add-branch-form'));
            const branchData = {
                name: formData.get('name'),
                address: formData.get('address'),
                phone: formData.get('phone'),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Validation
            if (!branchData.name || !branchData.address) {
                this.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            const branchId = 'branch_' + Date.now();
            await this.database.ref(`branches/${branchId}`).set(branchData);
            
            this.showMessage('تم إضافة الفرع بنجاح', 'success');
            document.getElementById('add-branch-form').reset();
            await this.loadBranches();
            
        } catch (error) {
            console.error('❌ Error adding branch:', error);
            this.showMessage('خطأ في إضافة الفرع', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async editBranch(branchId) {
        try {
            const snapshot = await this.database.ref(`branches/${branchId}`).once('value');
            const branch = snapshot.val();
            
            if (branch) {
                document.getElementById('edit-branch-id').value = branchId;
                document.getElementById('edit-branch-name').value = branch.name;
                document.getElementById('edit-branch-address').value = branch.address;
                document.getElementById('edit-branch-phone').value = branch.phone || '';
                
                document.getElementById('edit-branch-modal').classList.add('active');
            }
        } catch (error) {
            console.error('❌ Error loading branch for edit:', error);
            this.showMessage('خطأ في تحميل بيانات الفرع', 'error');
        }
    }

    async updateBranch() {
        try {
            this.showLoading();
            
            const branchId = document.getElementById('edit-branch-id').value;
            const formData = new FormData(document.getElementById('edit-branch-form'));
            
            const branchData = {
                name: formData.get('name'),
                address: formData.get('address'),
                phone: formData.get('phone'),
                updatedAt: new Date().toISOString()
            };

            // Get existing data to preserve createdAt
            const snapshot = await this.database.ref(`branches/${branchId}`).once('value');
            const existingData = snapshot.val();
            if (existingData && existingData.createdAt) {
                branchData.createdAt = existingData.createdAt;
            }

            await this.database.ref(`branches/${branchId}`).set(branchData);
            
            this.showMessage('تم تحديث الفرع بنجاح', 'success');
            this.closeEditModal();
            await this.loadBranches();
            
        } catch (error) {
            console.error('❌ Error updating branch:', error);
            this.showMessage('خطأ في تحديث الفرع', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async deleteBranch(branchId) {
        if (!confirm('هل أنت متأكد من حذف هذا الفرع؟')) {
            return;
        }

        try {
            this.showLoading();
            await this.database.ref(`branches/${branchId}`).remove();
            this.showMessage('تم حذف الفرع بنجاح', 'success');
            await this.loadBranches();
        } catch (error) {
            console.error('❌ Error deleting branch:', error);
            this.showMessage('خطأ في حذف الفرع', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Contact Info Management
    async loadContactInfo() {
        try {
            const snapshot = await this.database.ref('contactSection').once('value');
            const data = snapshot.val();
            
            if (data) {
                document.getElementById('contact-title').value = data.title || '';
                document.getElementById('contact-info-title').value = data.infoTitle || '';
                document.getElementById('contact-address').value = data.address || '';
                document.getElementById('contact-hours').value = data.hours || '';
            }
        } catch (error) {
            console.error('❌ Error loading contact info:', error);
        }
    }

    async saveContactInfo() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('contact-form'));
            const data = {
                title: formData.get('title'),
                infoTitle: formData.get('infoTitle'),
                address: formData.get('address'),
                hours: formData.get('hours'),
                updatedAt: new Date().toISOString()
            };

            await this.database.ref('contactSection').set(data);
            this.showMessage('تم حفظ معلومات التواصل بنجاح', 'success');
            
        } catch (error) {
            console.error('❌ Error saving contact info:', error);
            this.showMessage('خطأ في حفظ معلومات التواصل', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Gallery Management
    async loadGallery() {
        try {
            const snapshot = await this.database.ref('gallery').once('value');
            const data = snapshot.val();
            const container = document.getElementById('gallery-grid');
            const noData = document.getElementById('no-gallery');
            
            container.innerHTML = '';
            
            if (data && Object.keys(data).length > 0) {
                noData.style.display = 'none';
                
                Object.entries(data).forEach(([id, image]) => {
                    const imageElement = this.createGalleryElement(id, image);
                    container.appendChild(imageElement);
                });
            } else {
                noData.style.display = 'block';
            }
        } catch (error) {
            console.error('❌ Error loading gallery:', error);
        }
    }

    createGalleryElement(id, image) {
        const div = document.createElement('div');
        div.className = 'gallery-item';
        div.innerHTML = `
            <img src="${image.url}" alt="${this.escapeHtml(image.alt)}" loading="lazy">
            <p><strong>الوصف:</strong> ${this.escapeHtml(image.alt)}</p>
            <div class="branch-actions">
                <button class="admin-btn danger" onclick="adminPanel.deleteImage('${id}')">🗑️ حذف</button>
            </div>
        `;
        return div;
    }

    async uploadImage() {
        try {
            this.showLoading();
            
            const fileInput = document.getElementById('image-file');
            const altInput = document.getElementById('image-alt');
            
            if (!fileInput.files[0]) {
                this.showMessage('يرجى اختيار صورة', 'error');
                return;
            }

            const file = fileInput.files[0];
            const alt = altInput.value;

            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                this.showMessage('حجم الصورة يجب أن يكون أقل من 5MB', 'error');
                return;
            }

            // Upload to Firebase Storage
            const fileName = `gallery/${Date.now()}_${file.name}`;
            const storageRef = this.storage.ref(fileName);
            const uploadTask = await storageRef.put(file);
            const downloadURL = await uploadTask.ref.getDownloadURL();

            // Save to database
            const imageData = {
                url: downloadURL,
                alt: alt,
                fileName: fileName,
                uploadedAt: new Date().toISOString()
            };

            const imageId = 'img_' + Date.now();
            await this.database.ref(`gallery/${imageId}`).set(imageData);
            
            this.showMessage('تم رفع الصورة بنجاح', 'success');
            document.getElementById('upload-image-form').reset();
            await this.loadGallery();
            
        } catch (error) {
            console.error('❌ Error uploading image:', error);
            this.showMessage('خطأ في رفع الصورة', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async deleteImage(imageId) {
        if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
            return;
        }

        try {
            this.showLoading();
            
            // Get image data to delete from storage
            const snapshot = await this.database.ref(`gallery/${imageId}`).once('value');
            const imageData = snapshot.val();
            
            if (imageData && imageData.fileName) {
                // Delete from storage
                await this.storage.ref(imageData.fileName).delete();
            }
            
            // Delete from database
            await this.database.ref(`gallery/${imageId}`).remove();
            
            this.showMessage('تم حذف الصورة بنجاح', 'success');
            await this.loadGallery();
            
        } catch (error) {
            console.error('❌ Error deleting image:', error);
            this.showMessage('خطأ في حذف الصورة', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Settings Management
    async loadSettings() {
        try {
            const snapshot = await this.database.ref('siteSettings').once('value');
            const data = snapshot.val();
            
            if (data) {
                document.getElementById('site-email').value = data.contactEmail || '';
                document.getElementById('site-phone').value = data.contactPhone || '';
            }
        } catch (error) {
            console.error('❌ Error loading settings:', error);
        }
    }

    async saveSettings() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('settings-form'));
            const data = {
                contactEmail: formData.get('contactEmail'),
                contactPhone: formData.get('contactPhone'),
                updatedAt: new Date().toISOString()
            };

            await this.database.ref('siteSettings').set(data);
            this.showMessage('تم حفظ الإعدادات بنجاح', 'success');
            
        } catch (error) {
            console.error('❌ Error saving settings:', error);
            this.showMessage('خطأ في حفظ الإعدادات', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Users Management
    async loadUsers() {
        try {
            const snapshot = await this.database.ref('users').once('value');
            const data = snapshot.val();
            const tbody = document.getElementById('users-table');
            
            tbody.innerHTML = '';
            
            if (data && Object.keys(data).length > 0) {
                Object.entries(data).forEach(([id, user]) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${this.escapeHtml(user.name || 'غير محدد')}</td>
                        <td>${this.escapeHtml(user.email)}</td>
                        <td>${this.escapeHtml(user.phone || 'غير محدد')}</td>
                        <td><span class="badge ${user.role === 'admin' ? 'admin' : 'user'}">${user.role === 'admin' ? 'مدير' : 'مستخدم'}</span></td>
                        <td>${this.formatDate(user.createdAt)}</td>
                        <td>
                            ${user.email !== '<EMAIL>' ? 
                                `<button class="admin-btn danger" onclick="adminPanel.deleteUser('${id}')">حذف</button>` : 
                                '<span style="color: #666;">المدير الرئيسي</span>'
                            }
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="6" class="no-data">لا توجد مستخدمين مسجلين</td></tr>';
            }
        } catch (error) {
            console.error('❌ Error loading users:', error);
        }
    }

    async deleteUser(userId) {
        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            return;
        }

        try {
            this.showLoading();
            await this.database.ref(`users/${userId}`).remove();
            this.showMessage('تم حذف المستخدم بنجاح', 'success');
            await this.loadUsers();
        } catch (error) {
            console.error('❌ Error deleting user:', error);
            this.showMessage('خطأ في حذف المستخدم', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Banner Management
    async loadBannerSettings() {
        try {
            const snapshot = await this.database.ref('bannerSettings').once('value');
            const data = snapshot.val();

            if (data) {
                document.getElementById('banner-text').value = data.text || 'السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات';
                document.getElementById('banner-enabled').checked = data.enabled !== false;
            } else {
                // Set default values
                document.getElementById('banner-text').value = 'السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات';
                document.getElementById('banner-enabled').checked = true;
            }
        } catch (error) {
            console.error('❌ Error loading banner settings:', error);
        }
    }

    async saveBannerSettings() {
        try {
            this.showLoading();

            const formData = new FormData(document.getElementById('banner-form'));
            const data = {
                text: formData.get('bannerText'),
                enabled: formData.get('bannerEnabled') === 'on',
                updatedAt: new Date().toISOString()
            };

            await this.database.ref('bannerSettings').set(data);
            this.showMessage('تم حفظ إعدادات الشريط المتحرك بنجاح', 'success');

            // Update the banner on the main site
            this.updateMainSiteBanner(data);

        } catch (error) {
            console.error('❌ Error saving banner settings:', error);
            this.showMessage('خطأ في حفظ إعدادات الشريط المتحرك', 'error');
        } finally {
            this.hideLoading();
        }
    }

    updateMainSiteBanner(bannerData) {
        // Update banner in localStorage for immediate effect
        localStorage.setItem('bannerSettings', JSON.stringify(bannerData));

        // If main site is open in another tab, it will update automatically
        // through the dynamic content system
    }

    previewBanner() {
        const text = document.getElementById('banner-text').value;
        const enabled = document.getElementById('banner-enabled').checked;

        if (!enabled) {
            alert('الشريط المتحرك معطل حالياً');
            return;
        }

        if (!text.trim()) {
            alert('يرجى إدخال نص للشريط المتحرك');
            return;
        }

        // Create preview modal
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        `;

        modal.innerHTML = `
            <div style="background: linear-gradient(45deg, #667eea, #764ba2); padding: 1rem 0; overflow: hidden; position: relative; margin: 3rem 2rem; border-radius: 25px; box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3); border: 2px solid rgba(255, 255, 255, 0.2); height: 60px; display: flex; align-items: center; width: 80%; max-width: 800px;">
                <div style="width: 100%; overflow: hidden;">
                    <div style="display: flex; white-space: nowrap; animation: moveText 30s linear infinite; gap: 5rem;">
                        <span style="color: white; font-size: 1.3rem; font-weight: 700; text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4); letter-spacing: 1.2px; display: inline-block; min-width: max-content; position: relative; z-index: 3;">${text}</span>
                        <span style="color: white; font-size: 1.3rem; font-weight: 700; text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4); letter-spacing: 1.2px; display: inline-block; min-width: max-content; position: relative; z-index: 3;">${text}</span>
                        <span style="color: white; font-size: 1.3rem; font-weight: 700; text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4); letter-spacing: 1.2px; display: inline-block; min-width: max-content; position: relative; z-index: 3;">${text}</span>
                    </div>
                </div>
            </div>
            <button onclick="this.parentElement.remove()" style="margin-top: 2rem; padding: 1rem 2rem; background: #667eea; color: white; border: none; border-radius: 10px; cursor: pointer; font-size: 1rem;">إغلاق المعاينة</button>
        `;

        document.body.appendChild(modal);
    }

    // Gallery Images Management
    async loadGalleryImages() {
        try {
            const snapshot = await this.database.ref('galleryImages').once('value');
            const data = snapshot.val();
            this.displayGalleryImages(data);
        } catch (error) {
            console.error('❌ Error loading gallery images:', error);
        }
    }

    displayGalleryImages(data) {
        const container = document.getElementById('gallery-images-grid');
        container.innerHTML = '';

        if (data && Object.keys(data).length > 0) {
            const images = Object.entries(data).sort((a, b) =>
                new Date(b[1].uploadedAt) - new Date(a[1].uploadedAt)
            );

            images.forEach(([id, image]) => {
                const imageElement = this.createGalleryImageElement(id, image);
                container.appendChild(imageElement);
            });
        } else {
            container.innerHTML = '<div class="no-data"><p>لا توجد صور في المعرض</p></div>';
        }
    }

    createGalleryImageElement(id, image) {
        const div = document.createElement('div');
        div.className = 'gallery-image-item';
        div.innerHTML = `
            <img src="${image.url}" alt="${image.title}" class="gallery-image-preview">
            <div class="gallery-image-info">
                <h4 class="gallery-image-title">${image.title}</h4>
                <p class="gallery-image-description">${image.description}</p>
                <div class="gallery-image-meta">
                    <span class="gallery-image-category">${this.getCategoryName(image.category)}</span>
                    ${image.featured ? '<span class="gallery-image-featured">مميزة</span>' : ''}
                </div>
                <div class="gallery-image-actions">
                    <button class="edit-btn" onclick="editGalleryImage('${id}')">✏️ تعديل</button>
                    <button class="delete-btn" onclick="deleteGalleryImage('${id}')">🗑️ حذف</button>
                </div>
            </div>
        `;
        return div;
    }

    getCategoryName(category) {
        const categories = {
            'services': 'خدماتنا',
            'installation': 'التركيب',
            'repair': 'الإصلاح',
            'products': 'منتجاتنا'
        };
        return categories[category] || category;
    }

    async uploadGalleryImage() {
        try {
            this.showLoading();

            const form = document.getElementById('upload-image-form');
            const formData = new FormData(form);
            const file = formData.get('imageFile');

            if (!file) {
                this.showMessage('يرجى اختيار صورة', 'error');
                return;
            }

            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                this.showMessage('حجم الصورة يجب أن يكون أقل من 5MB', 'error');
                return;
            }

            // Show upload progress
            const progressContainer = document.getElementById('upload-progress');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            progressContainer.style.display = 'block';

            // Convert file to base64 for Firebase storage
            const base64 = await this.fileToBase64(file);

            const imageData = {
                title: formData.get('imageTitle'),
                description: formData.get('imageDescription'),
                category: formData.get('imageCategory'),
                featured: formData.get('imageFeatured') === 'on',
                url: base64,
                fileName: file.name,
                fileSize: file.size,
                uploadedAt: new Date().toISOString()
            };

            // Simulate upload progress
            for (let i = 0; i <= 100; i += 10) {
                progressFill.style.width = i + '%';
                progressText.textContent = i + '%';
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Save to Firebase
            const imageRef = this.database.ref('galleryImages').push();
            await imageRef.set(imageData);

            this.showMessage('تم رفع الصورة بنجاح', 'success');
            form.reset();
            progressContainer.style.display = 'none';
            this.loadGalleryImages();

        } catch (error) {
            console.error('❌ Error uploading image:', error);
            this.showMessage('خطأ في رفع الصورة', 'error');
        } finally {
            this.hideLoading();
        }
    }

    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    async deleteGalleryImage(imageId) {
        if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
            return;
        }

        try {
            this.showLoading();
            await this.database.ref(`galleryImages/${imageId}`).remove();
            this.showMessage('تم حذف الصورة بنجاح', 'success');
            this.loadGalleryImages();
        } catch (error) {
            console.error('❌ Error deleting image:', error);
            this.showMessage('خطأ في حذف الصورة', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Messages Management
    async loadMessages() {
        try {
            const snapshot = await this.database.ref('contactForms').once('value');
            const data = snapshot.val();
            const container = document.getElementById('messages-container');

            container.innerHTML = '';

            if (data && Object.keys(data).length > 0) {
                const messages = Object.entries(data).sort((a, b) =>
                    new Date(b[1].submittedAt) - new Date(a[1].submittedAt)
                );

                messages.forEach(([id, message]) => {
                    const messageElement = this.createMessageElement(id, message);
                    container.appendChild(messageElement);
                });
            } else {
                container.innerHTML = '<div class="no-data"><p>لا توجد رسائل واردة</p></div>';
            }
        } catch (error) {
            console.error('❌ Error loading messages:', error);
        }
    }

    createMessageElement(id, message) {
        const div = document.createElement('div');
        div.className = 'message-item';
        div.innerHTML = `
            <div class="message-header">
                <span class="message-sender">${this.escapeHtml(message.name)} - ${this.escapeHtml(message.email)}</span>
                <span class="message-date">${this.formatDate(message.submittedAt)}</span>
            </div>
            <p><strong>الهاتف:</strong> ${this.escapeHtml(message.phone)}</p>
            <div class="message-content">${this.escapeHtml(message.message)}</div>
            <div class="branch-actions" style="margin-top: 1rem;">
                <button class="admin-btn danger" onclick="adminPanel.deleteMessage('${id}')">🗑️ حذف</button>
            </div>
        `;
        return div;
    }

    async deleteMessage(messageId) {
        if (!confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
            return;
        }

        try {
            this.showLoading();
            await this.database.ref(`contactForms/${messageId}`).remove();
            this.showMessage('تم حذف الرسالة بنجاح', 'success');
            await this.loadMessages();
        } catch (error) {
            console.error('❌ Error deleting message:', error);
            this.showMessage('خطأ في حذف الرسالة', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Utility Functions
    showLoading() {
        this.isLoading = true;
        document.getElementById('loading-overlay').classList.remove('hidden');
    }

    hideLoading() {
        this.isLoading = false;
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    showMessage(message, type) {
        const messageEl = document.getElementById('admin-message');
        messageEl.textContent = message;
        messageEl.className = `admin-message ${type}`;
        messageEl.style.display = 'block';

        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 5000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        if (!dateString) return 'غير محدد';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
    }

    closeEditModal() {
        document.getElementById('edit-branch-modal').classList.remove('active');
    }

    // Hero Images Management
    async loadHeroImages() {
        try {
            const snapshot = await this.database.ref('heroImages').once('value');
            const data = snapshot.val();

            if (data) {
                // Update preview images with stored URLs
                Object.entries(data).forEach(([imageType, imageData]) => {
                    if (imageData.url) {
                        const previewImg = document.getElementById(`preview-${imageType}`);
                        if (previewImg) {
                            previewImg.src = imageData.url;
                        }
                    }
                });
            }
        } catch (error) {
            console.error('Error loading hero images:', error);
        }
    }

    async uploadHeroImage(imageType) {
        try {
            const fileInput = document.getElementById(`file-${imageType}`);
            const file = fileInput.files[0];

            if (!file) {
                this.showMessage('يرجى اختيار صورة أولاً', 'error');
                return;
            }

            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                this.showMessage('حجم الصورة يجب أن يكون أقل من 5MB', 'error');
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                this.showMessage('نوع الملف غير مدعوم. يرجى اختيار صورة JPG, PNG أو GIF', 'error');
                return;
            }

            console.log(`Starting upload for ${imageType}...`);
            this.showLoading();

            // Convert file to Base64 (same as gallery and service images)
            console.log('Converting image to Base64...');
            const base64Data = await this.fileToBase64(file);
            console.log('Base64 conversion completed');

            // Create image data object (same structure as service images)
            const imageData = {
                url: base64Data,
                fileName: file.name,
                uploadedAt: new Date().toISOString(),
                uploadedBy: this.currentUser ? this.currentUser.uid : 'admin',
                imageType: imageType,
                fileSize: file.size,
                fileType: file.type
            };

            console.log('Saving to database:', imageType);

            // Save to database (same as service images)
            await this.database.ref(`heroImages/${imageType}`).set(imageData);
            console.log('Data saved to database successfully');

            this.showMessage(`تم رفع صورة ${imageType} بنجاح`, 'success');

            // Update preview image
            document.getElementById(`preview-${imageType}`).src = base64Data;

            // Hide upload button
            document.getElementById(`upload-${imageType}`).style.display = 'none';

            // Clear file input
            fileInput.value = '';

        } catch (error) {
            console.error('Error uploading hero image:', error);
            this.showMessage(`خطأ في رفع الصورة: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Service Images Management
    async loadServiceImages() {
        try {
            const snapshot = await this.database.ref('serviceImages').once('value');
            const data = snapshot.val();

            if (data) {
                // Update preview images with stored URLs and text
                Object.entries(data).forEach(([serviceType, imageData]) => {
                    if (imageData.url) {
                        const previewImg = document.getElementById(`preview-${serviceType}-service`);
                        if (previewImg) {
                            previewImg.src = imageData.url;
                        }

                        // Update filename display
                        const filenameSpan = document.querySelector(`[data-service="${serviceType}"] .image-filename`);
                        if (filenameSpan && imageData.fileName) {
                            filenameSpan.textContent = imageData.fileName;
                        }

                        // Update title and description
                        if (imageData.title) {
                            const titleElement = document.getElementById(`title-${serviceType}-service`);
                            const titleInput = document.getElementById(`title-input-${serviceType}`);
                            if (titleElement) titleElement.textContent = imageData.title;
                            if (titleInput) titleInput.value = imageData.title;
                        }

                        if (imageData.description) {
                            const descElement = document.getElementById(`desc-${serviceType}-service`);
                            const descInput = document.getElementById(`desc-input-${serviceType}`);
                            if (descElement) descElement.textContent = imageData.description;
                            if (descInput) descInput.value = imageData.description;
                        }
                    }
                });
            }
        } catch (error) {
            console.error('Error loading service images:', error);
        }
    }

    async uploadServiceImage(serviceType) {
        try {
            const fileInput = document.getElementById(`file-${serviceType}-service`);
            const file = fileInput.files[0];

            if (!file) {
                this.showMessage('يرجى اختيار صورة أولاً', 'error');
                return;
            }

            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
                this.showMessage('حجم الصورة يجب أن يكون أقل من 5MB', 'error');
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                this.showMessage('نوع الملف غير مدعوم. يرجى اختيار صورة JPG, PNG أو GIF', 'error');
                return;
            }

            console.log(`Starting upload for ${serviceType}...`);
            this.showLoading();

            // Get title and description from inputs (make title optional)
            const titleInput = document.getElementById(`title-input-${serviceType}`);
            const descInput = document.getElementById(`desc-input-${serviceType}`);
            const title = titleInput && titleInput.value.trim() ? titleInput.value.trim() : this.getServiceDisplayName(serviceType);
            const description = descInput && descInput.value.trim() ? descInput.value.trim() : '';

            // Convert file to Base64 (same as gallery images)
            console.log('Converting image to Base64...');
            const base64Data = await this.fileToBase64(file);
            console.log('Base64 conversion completed');

            // Create image data object (same structure as gallery images)
            const imageData = {
                url: base64Data,
                fileName: file.name,
                title: title,
                description: description,
                uploadedAt: new Date().toISOString(),
                uploadedBy: this.currentUser ? this.currentUser.uid : 'admin',
                serviceType: serviceType,
                fileSize: file.size,
                fileType: file.type
            };

            console.log('Saving to database:', serviceType);

            // Save to database (same as gallery images)
            await this.database.ref(`serviceImages/${serviceType}`).set(imageData);
            console.log('Data saved to database successfully');

            // Update UI immediately
            this.updateServiceImageUI(serviceType, base64Data, file.name, title, description);

            this.showMessage(`تم رفع صورة ${this.getServiceDisplayName(serviceType)} بنجاح`, 'success');

            // Hide upload button
            const uploadBtn = document.getElementById(`upload-${serviceType}-service`);
            if (uploadBtn) {
                uploadBtn.style.display = 'none';
            }

            // Clear file input
            fileInput.value = '';

        } catch (error) {
            console.error('Error uploading service image:', error);
            this.showMessage(`خطأ في رفع الصورة: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Helper function to update UI after successful upload
    updateServiceImageUI(serviceType, downloadURL, fileName, title, description) {
        try {
            // Update preview image
            const previewImg = document.getElementById(`preview-${serviceType}-service`);
            if (previewImg) {
                previewImg.src = downloadURL;
                console.log(`Updated preview image for ${serviceType}`);
            }

            // Update filename display
            const filenameSpan = document.querySelector(`[data-service="${serviceType}"] .image-filename`);
            if (filenameSpan) {
                filenameSpan.textContent = fileName;
                console.log(`Updated filename display for ${serviceType}`);
            }

            // Update title and description in UI
            const titleElement = document.getElementById(`title-${serviceType}-service`);
            const descElement = document.getElementById(`desc-${serviceType}-service`);
            if (titleElement) {
                titleElement.textContent = title;
                console.log(`Updated title for ${serviceType}`);
            }
            if (descElement) {
                descElement.textContent = description;
                console.log(`Updated description for ${serviceType}`);
            }

        } catch (error) {
            console.error('Error updating UI:', error);
        }
    }

    getServiceDisplayName(serviceType) {
        const serviceNames = {
            'car2': 'خدمات زجاج السيارات',
            'car4': 'تركيب زجاج السيارات',
            'car6': 'إصلاح زجاج السيارات'
        };
        return serviceNames[serviceType] || serviceType;
    }

    // Update service text only (without image)
    async updateServiceText(serviceType) {
        try {
            const titleInput = document.getElementById(`title-input-${serviceType}`);
            const descInput = document.getElementById(`desc-input-${serviceType}`);

            if (!titleInput || !descInput) {
                this.showMessage('خطأ في العثور على حقول النص', 'error');
                return;
            }

            // Make title optional - use default if empty
            const title = titleInput.value.trim() || this.getServiceDisplayName(serviceType);
            const description = descInput.value.trim();

            this.showLoading();

            // Get current service data
            const snapshot = await this.database.ref(`serviceImages/${serviceType}`).once('value');
            const currentData = snapshot.val() || {};

            // Update with new text data
            const updatedData = {
                ...currentData,
                title: title,
                description: description,
                updatedAt: new Date().toISOString(),
                updatedBy: this.currentUser ? this.currentUser.uid : 'admin'
            };

            // If no image exists, add default image path (Base64 format like gallery)
            if (!updatedData.url) {
                // Use default image path for now, will be replaced when user uploads image
                updatedData.url = `img/${serviceType}.png`;
                updatedData.fileName = `${serviceType}.png`;
                updatedData.isDefault = true; // Mark as default image
            }

            // Save to database
            await this.database.ref(`serviceImages/${serviceType}`).set(updatedData);

            // Update UI elements
            const titleElement = document.getElementById(`title-${serviceType}-service`);
            const descElement = document.getElementById(`desc-${serviceType}-service`);

            if (titleElement) titleElement.textContent = title;
            if (descElement) descElement.textContent = description;

            // Update input field with the actual saved title
            if (titleInput) titleInput.value = title;

            this.showMessage(`تم تحديث نص ${this.getServiceDisplayName(serviceType)} بنجاح`, 'success');

        } catch (error) {
            console.error('Error updating service text:', error);
            this.showMessage('خطأ في تحديث النص', 'error');
        } finally {
            this.hideLoading();
        }
    }
}

// Global Functions
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from all menu items
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Add active class to clicked menu item
    document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');
}

function previewSite() {
    window.open('index.html', '_blank');
}

function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        firebase.auth().signOut().then(() => {
            localStorage.removeItem('user');
            window.location.href = 'login.html';
        });
    }
}

function closeEditModal() {
    document.getElementById('edit-branch-modal').classList.remove('active');
}

function loadAboutInfo() {
    if (adminPanel) {
        adminPanel.loadAboutInfo();
    }
}

function loadBannerSettings() {
    if (adminPanel) {
        adminPanel.loadBannerSettings();
    }
}

function previewBanner() {
    if (adminPanel) {
        adminPanel.previewBanner();
    }
}

function clearImageForm() {
    document.getElementById('upload-image-form').reset();
    document.getElementById('upload-progress').style.display = 'none';
}

function filterGalleryImages() {
    const searchTerm = document.getElementById('gallery-search').value.toLowerCase();
    const categoryFilter = document.getElementById('category-filter').value;
    const imageItems = document.querySelectorAll('.gallery-image-item');

    imageItems.forEach(item => {
        const title = item.querySelector('.gallery-image-title').textContent.toLowerCase();
        const description = item.querySelector('.gallery-image-description').textContent.toLowerCase();
        const category = item.querySelector('.gallery-image-category').textContent;

        const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
        const matchesCategory = !categoryFilter || category === adminPanel.getCategoryName(categoryFilter);

        if (matchesSearch && matchesCategory) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

function editGalleryImage(imageId) {
    // TODO: Implement edit functionality
    alert('وظيفة التعديل قيد التطوير');
}

function deleteGalleryImage(imageId) {
    if (adminPanel) {
        adminPanel.deleteGalleryImage(imageId);
    }
}

// Hero Images Management Functions
function previewHeroImage(imageType, input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file
        if (!validateImageFile(file)) {
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById(`preview-${imageType}`).src = e.target.result;
            document.getElementById(`upload-${imageType}`).style.display = 'inline-flex';
        };
        reader.readAsDataURL(file);
    }
}

function uploadHeroImage(imageType) {
    if (adminPanel) {
        adminPanel.uploadHeroImage(imageType);
    }
}

// Service Images Management Functions
function previewServiceImage(serviceType, input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file
        if (!validateImageFile(file)) {
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById(`preview-${serviceType}-service`).src = e.target.result;
            document.getElementById(`upload-${serviceType}-service`).style.display = 'inline-flex';
        };
        reader.readAsDataURL(file);
    }
}

function uploadServiceImage(serviceType) {
    if (adminPanel) {
        adminPanel.uploadServiceImage(serviceType);
    }
}

function updateServiceText(serviceType) {
    if (adminPanel) {
        adminPanel.updateServiceText(serviceType);
    }
}

function validateImageFile(file) {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يرجى اختيار صورة JPG, PNG أو GIF');
        return false;
    }

    // Check file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
        alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
        return false;
    }

    return true;
}

// Initialize Admin Panel
let adminPanel;
document.addEventListener('DOMContentLoaded', () => {
    adminPanel = new AdminPanel();
});
